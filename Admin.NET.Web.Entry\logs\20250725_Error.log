fail: 2025-07-25 09:45:07.1927702 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:45:36.9066525 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:06.7889092 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:36.7980756 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:06.8489019 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:36.8529151 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:06.8696098 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:36.8274020 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:06.7559667 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:36.7480708 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:06.8133373 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:36.7724847 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:09.9691301 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:40.6598621 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:09.9716569 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:36.7442553 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:06.6969114 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:36.7190091 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:06.7201971 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:36.7178650 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:06.7249969 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:36.6935128 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:06.7484941 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:36.7138675 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:06.7235985 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:36.7550381 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:06.7480094 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:37.0719558 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:37.3575796 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:06.7183425 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:07.0192609 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:36.7438421 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:37.0303109 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:06.7271520 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:07.0482825 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:36.7478195 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:37.0377981 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.6905104 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.9956585 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:36.7641161 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:37.1007985 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:06.7040270 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:07.0953695 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:36.7367888 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:37.0452066 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:06.8106381 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:07.1216770 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.6970269 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.9601406 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:06.7247849 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:07.0186301 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.6696932 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.9358849 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.7193976 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.9787677 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.7220124 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.9951725 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.6788694 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.9521028 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:38.7419753 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:39.0372895 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:09.0776538 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:09.3408315 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:36.7311067 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:37.0558376 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:06.7691877 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:07.0548177 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.6943208 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.9510284 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.7141461 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.9659578 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.7247504 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.9905327 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.7052463 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.9802020 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:36.7611750 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:37.0480618 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.6990920 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.9608381 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
