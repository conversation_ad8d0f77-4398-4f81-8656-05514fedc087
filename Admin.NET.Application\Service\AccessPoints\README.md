# AP状态实时同步WebSocket功能说明

## 功能概述

本功能实现了AP（Access Point）状态的实时同步和WebSocket推送，包括以下核心组件：

1. **APSyncBackgroundService** - 后台服务，负责定时同步AP状态
2. **APStatusHub** - SignalR Hub，提供WebSocket连接和实时推送
3. **EstablishWebSocketConnectionAsync** - AccessPointsService中的方法，用于启动WebSocket功能

## 架构设计

### 1. 后台同步服务 (APSyncBackgroundService)

- **功能**：每30秒自动同步一次AP状态
- **流程**：
  1. 调用第三方API获取最新AP数据
  2. 与本地数据库中的AP数据进行比较
  3. 更新有变化的AP记录到数据库
  4. 通过SignalR推送状态变更通知

- **特性**：
  - 自动重试机制
  - 超时控制（25秒）
  - 批量更新优化
  - 详细的日志记录

### 2. SignalR Hub (APStatusHub)

- **端点**：`/hubs/apstatus`
- **功能**：
  - 客户端连接管理
  - 分组管理（AP状态组）
  - 实时状态推送
  - 当前状态查询

- **客户端方法**：
  - `APStatusUpdated` - 接收AP状态更新
  - `CurrentAPStatus` - 接收当前AP状态响应

- **服务端方法**：
  - `JoinAPStatusGroup` - 加入AP状态组
  - `LeaveAPStatusGroup` - 离开AP状态组
  - `RequestCurrentAPStatus` - 请求当前AP状态

## 使用方法

### 1. 服务端配置

服务已自动注册，无需额外配置：

```csharp
// 在 Admin.NET.Application/Startup.cs 中已自动注册
services.AddHostedService<APSyncBackgroundService>();
```

### 2. 客户端连接

#### JavaScript示例

```javascript
// 建立连接
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/apstatus")
    .withAutomaticReconnect()
    .build();

// 监听AP状态更新
connection.on("APStatusUpdated", function (statusUpdates) {
    console.log("收到AP状态更新：", statusUpdates);
    // 处理状态更新逻辑
});

// 启动连接
await connection.start();

// 加入AP状态组
await connection.invoke("JoinAPStatusGroup");

// 请求当前状态
await connection.invoke("RequestCurrentAPStatus");
```

#### C# 客户端示例

```csharp
using Microsoft.AspNetCore.SignalR.Client;

var connection = new HubConnectionBuilder()
    .WithUrl("https://your-server/hubs/apstatus")
    .WithAutomaticReconnect()
    .Build();

// 监听状态更新
connection.On<List<object>>("APStatusUpdated", (statusUpdates) =>
{
    Console.WriteLine($"收到AP状态更新，数量：{statusUpdates.Count}");
    // 处理状态更新
});

// 启动连接
await connection.StartAsync();

// 加入组
await connection.InvokeAsync("JoinAPStatusGroup");
```

### 3. 数据格式

#### AP状态更新数据格式

```json
[
  {
    "MacAddress": "00:11:22:33:44:55",
    "ApName": "AP-001",
    "Status": 1,
    "IpAddress": "*************",
    "FirmwareVersion": "1.0.0",
    "UpdateTime": "2024-01-01T12:00:00"
  }
]
```

#### 状态值说明

- `1` - 在线
- `2` - 离线
- `3` - 异常

## 演示页面

访问 `/ap-status-demo.html` 可以查看实时监控演示页面，该页面展示了：

- WebSocket连接状态
- 实时AP状态卡片
- 连接控制按钮
- 操作日志

## 配置参数

### 同步间隔

默认30秒同步一次，可在 `APSyncBackgroundService.cs` 中修改：

```csharp
private readonly TimeSpan _syncInterval = TimeSpan.FromSeconds(30);
```

### 超时设置

默认25秒超时，可在同步方法中调整：

```csharp
using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(25));
```

### 分页大小

默认每页50条记录，可在获取数据方法中修改：

```csharp
const int pageSize = 50;
```

## 日志记录

系统提供详细的日志记录，包括：

- 连接状态变化
- 同步操作进度
- 错误和异常信息
- 性能统计数据

日志级别：
- `Information` - 重要操作和统计信息
- `Debug` - 详细的调试信息
- `Warning` - 警告信息（如超时）
- `Error` - 错误和异常

## 性能优化

1. **批量更新**：使用 `UpdateRangeAsync` 批量更新数据库
2. **分页获取**：分页获取第三方数据，避免内存溢出
3. **增量同步**：只更新有变化的AP记录
4. **连接复用**：SignalR自动重连机制
5. **内存优化**：及时释放资源，使用 `using` 语句

## 故障排除

### 常见问题

1. **连接失败**
   - 检查SignalR服务是否正确配置
   - 确认Hub端点路径正确
   - 检查防火墙和网络设置

2. **同步不工作**
   - 检查后台服务是否启动
   - 查看日志中的错误信息
   - 确认第三方API可访问

3. **数据不更新**
   - 检查数据库连接
   - 确认AP MAC地址匹配
   - 查看同步日志

### 调试建议

1. 启用详细日志记录
2. 使用演示页面测试连接
3. 检查网络连接和API响应
4. 监控数据库更新操作

## 扩展功能

### 自定义过滤器

可以在同步逻辑中添加自定义过滤器：

```csharp
// 只同步特定状态的AP
var filteredAPs = thirdPartyAPs.Where(ap => 
    ap.Status == "online" || ap.Status == "offline").ToList();
```

### 自定义通知

可以扩展Hub方法，支持更多类型的通知：

```csharp
public async Task NotifyAPAlert(string macAddress, string alertMessage)
{
    await Clients.All.SendAsync("APAlert", macAddress, alertMessage);
}
```

### 权限控制

可以添加权限验证：

```csharp
[Authorize(Roles = "Admin")]
public class APStatusHub : Hub<IAPStatusHub>
{
    // Hub方法
}
```

## 技术栈

- **ASP.NET Core** - Web框架
- **SignalR** - 实时通信
- **SqlSugar** - ORM框架
- **IHostedService** - 后台服务
- **Furion** - 应用框架

## 版本要求

- .NET 6.0 或更高版本
- SignalR 6.0 或更高版本
- 支持WebSocket的浏览器

---

如有问题或建议，请联系开发团队。