fail: 2025-07-25 09:45:07.0473963 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:45:36.7372273 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:06.6859328 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:36.7169899 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:06.7548524 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:36.7698173 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:06.7731352 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:36.7349482 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:06.6863428 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:36.6703071 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:06.7416181 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:36.7042842 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:09.8125365 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:40.5505110 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:09.9034132 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:36.6841195 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:06.6355283 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:36.6586039 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:06.6578481 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:36.6523859 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:06.6516660 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:36.6293379 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:06.6858045 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:36.6518994 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:06.6570367 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:36.6883515 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:06.6868790 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:37.0135050 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:37.2839689 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:06.6596622 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:06.9422414 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:36.6853963 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:36.9590256 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:06.6594160 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:06.9737302 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:36.6839437 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:36.9646582 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.6280043 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.9201770 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:36.6961491 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:37.0163157 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:06.6385392 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:06.9892259 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:36.6717100 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:36.9687577 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:06.7473758 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:07.0438906 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.6433126 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.8933135 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:06.6657711 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:06.9489267 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.6145414 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.8709266 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.6660002 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.9167161 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.6655228 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.9283471 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.6226949 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.8878010 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:38.6847634 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:38.9687084 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:09.0217019 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:09.2775524 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:36.6514589 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:36.9800909 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:06.7026524 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:06.9849789 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.6358450 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.8896725 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.6595657 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.9030446 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.6615808 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.9247246 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.6476283 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.9115115 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:36.6956624 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:36.9683779 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.6455596 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.8957240 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
