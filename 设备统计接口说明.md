# 设备统计接口说明

## 概述

本文档描述了蓝牙桌牌设备统计功能的接口实现，包括基础统计、详细统计和条件查询等功能。

## 接口列表

### 1. 基础设备统计

**接口地址：** `GET /api/devices/Statistics`

**功能描述：** 获取设备的基础统计信息

**返回数据结构：**
```json
{
  "TotalDevices": 100,          // 设备总数
  "OnlineDevices": 85,          // 在线设备数量
  "OfflineDevices": 15,         // 离线设备数量
  "LowBatteryDevices": 8,       // 低电量设备数量（<20%）
  "WeakSignalDevices": 12,      // 弱信号设备数量（<-80dBm）
  "OnlineRate": 85.00,          // 在线率（百分比）
  "LowBatteryRate": 8.00,       // 低电量设备占比（百分比）
  "WeakSignalRate": 12.00       // 弱信号设备占比（百分比）
}
```

### 2. 详细设备统计

**接口地址：** `GET /api/devices/DetailedStatistics`

**功能描述：** 获取设备的详细统计信息，包括按类型分组、电量分布、信号分布等

**返回数据结构：**
```json
{
  "BasicStatistics": {
    // 基础统计信息（同上）
  },
  "DeviceTypeStatistics": [
    {
      "DeviceType": 1,
      "DeviceTypeName": "姓名桌牌",
      "TotalCount": 60,
      "OnlineCount": 50,
      "OfflineCount": 10,
      "LowBatteryCount": 5,
      "WeakSignalCount": 8
    },
    {
      "DeviceType": 2,
      "DeviceTypeName": "价格标签",
      "TotalCount": 40,
      "OnlineCount": 35,
      "OfflineCount": 5,
      "LowBatteryCount": 3,
      "WeakSignalCount": 4
    }
  ],
  "BatteryDistribution": {
    "Battery0To20": 8,           // 电量0-20%的设备数量
    "Battery21To50": 15,         // 电量21-50%的设备数量
    "Battery51To80": 35,         // 电量51-80%的设备数量
    "Battery81To100": 42,        // 电量81-100%的设备数量
    "AverageBattery": 68.50      // 平均电量
  },
  "SignalDistribution": {
    "SignalBelow80": 12,         // 信号<-80dBm的设备数量
    "Signal80To60": 25,          // 信号-80到-60dBm的设备数量
    "Signal60To40": 40,          // 信号-60到-40dBm的设备数量
    "SignalAbove40": 23,         // 信号>-40dBm的设备数量
    "AverageSignal": -55.30      // 平均信号强度
  }
}
```

### 3. 条件查询设备统计

**接口地址：** `POST /api/devices/StatisticsByCondition`

**功能描述：** 根据指定条件获取设备统计信息

**请求参数：**
```json
{
  "StartTime": "2024-01-01T00:00:00",  // 开始时间（可选）
  "EndTime": "2024-12-31T23:59:59",    // 结束时间（可选）
  "DeviceType": 1,                     // 设备类型过滤（可选，1：姓名桌牌；2：价格标签）
  "ApId": 123                          // 网关ID过滤（可选）
}
```

**返回数据：** 同详细设备统计接口

## 统计规则说明

### 设备状态定义
- **在线设备：** status = 0
- **离线设备：** status = 1
- **低电量设备：** battery_level < 20%
- **弱信号设备：** signal_strength < -80dBm

### 设备类型定义
- **1：** 姓名桌牌
- **2：** 价格标签

### 电量分布区间
- **0-20%：** 低电量，需要充电
- **21-50%：** 中低电量，建议关注
- **51-80%：** 正常电量
- **81-100%：** 高电量

### 信号强度分布区间
- **< -80dBm：** 弱信号，可能影响通信
- **-80 到 -60dBm：** 一般信号
- **-60 到 -40dBm：** 良好信号
- **> -40dBm：** 强信号

## 示例控制器

项目中还提供了一个示例控制器 `DeviceStatisticsController`，包含以下额外接口：

### 设备统计概览
**接口地址：** `GET /api/DeviceStatistics/overview`

### 设备详细统计
**接口地址：** `GET /api/DeviceStatistics/detailed`

### 条件查询统计
**接口地址：** `POST /api/DeviceStatistics/by-condition`

### 设备健康状况摘要
**接口地址：** `GET /api/DeviceStatistics/health-summary`

返回设备健康状况评估和维护建议。

## 使用建议

1. **定期监控：** 建议每小时调用基础统计接口，监控设备整体状况
2. **详细分析：** 每日调用详细统计接口，分析设备分布和趋势
3. **条件查询：** 根据需要使用条件查询，分析特定时间段或特定类型设备的状况
4. **告警设置：** 建议设置告警阈值：
   - 在线率 < 80% 时发出警告
   - 低电量设备占比 > 20% 时发出警告
   - 弱信号设备占比 > 30% 时发出警告

## 测试

项目包含了完整的单元测试，位于 `Admin.NET.Test/Service/DevicesServiceTest.cs`，可以运行测试验证功能的正确性。

## 注意事项

1. 所有统计都基于未删除的设备（IsDelete = false）
2. 百分比计算保留两位小数
3. 当设备总数为0时，所有比率返回0
4. 平均值计算会排除空值（null）
5. 接口包含完整的异常处理和日志记录
