// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Furion.InstantMessaging;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Application;

/// <summary>
/// AP状态实时推送Hub
/// </summary>
[MapHub("/hubs/apStatus")]
public class APStatusHub : Hub<IAPStatusHub>
{
    private readonly ILogger<APStatusHub> _logger;

    public APStatusHub(ILogger<APStatusHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时触发
    /// </summary>
    /// <returns></returns>
    public override async Task OnConnectedAsync()
    {
        var connectionId = Context.ConnectionId;
        var userIdentifier = Context.UserIdentifier;
        
        _logger.LogInformation("客户端连接到AP状态Hub，连接ID: {ConnectionId}, 用户: {UserIdentifier}", 
            connectionId, userIdentifier);
        
        // 可以在这里加入到特定的组，比如按租户分组
        // await Groups.AddToGroupAsync(connectionId, "AP_STATUS_GROUP");
        
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时触发
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <returns></returns>
    public override async Task OnDisconnectedAsync(Exception exception)
    {
        var connectionId = Context.ConnectionId;
        var userIdentifier = Context.UserIdentifier;
        
        _logger.LogInformation("客户端从AP状态Hub断开连接，连接ID: {ConnectionId}, 用户: {UserIdentifier}, 异常: {Exception}", 
            connectionId, userIdentifier, exception?.Message);
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 客户端请求加入AP状态监听组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    public async Task JoinAPStatusGroup(string groupName = "AP_STATUS_GROUP")
    {
        var connectionId = Context.ConnectionId;
        await Groups.AddToGroupAsync(connectionId, groupName);
        
        _logger.LogDebug("客户端加入AP状态监听组，连接ID: {ConnectionId}, 组名: {GroupName}", 
            connectionId, groupName);
        
        await Clients.Caller.JoinedGroup(groupName);
    }

    /// <summary>
    /// 客户端请求离开AP状态监听组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    public async Task LeaveAPStatusGroup(string groupName = "AP_STATUS_GROUP")
    {
        var connectionId = Context.ConnectionId;
        await Groups.RemoveFromGroupAsync(connectionId, groupName);
        
        _logger.LogDebug("客户端离开AP状态监听组，连接ID: {ConnectionId}, 组名: {GroupName}", 
            connectionId, groupName);
        
        await Clients.Caller.LeftGroup(groupName);
    }

    /// <summary>
    /// 客户端请求获取当前AP状态
    /// </summary>
    /// <returns></returns>
    public async Task RequestCurrentAPStatus()
    {
        try
        {
            var accessPointsService = Context.GetHttpContext()?.RequestServices.GetService<AccessPointsService>();
            if (accessPointsService != null)
            {
                // 这里可以调用服务获取当前所有AP状态
                // 由于AccessPointsService中的方法大多是私有的，这里只是示例
                await Clients.Caller.CurrentAPStatusRequested("请求已接收，正在获取最新状态...");
                
                _logger.LogDebug("客户端请求当前AP状态，连接ID: {ConnectionId}", Context.ConnectionId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端AP状态请求时发生异常");
            await Clients.Caller.Error("获取AP状态时发生错误");
        }
    }
}

/// <summary>
/// AP状态Hub接口
/// </summary>
public interface IAPStatusHub
{
    /// <summary>
    /// AP状态更新通知
    /// </summary>
    /// <param name="statusUpdates">状态更新列表</param>
    /// <returns></returns>
    Task APStatusUpdated(object statusUpdates);

    /// <summary>
    /// 加入组成功通知
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    Task JoinedGroup(string groupName);

    /// <summary>
    /// 离开组成功通知
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <returns></returns>
    Task LeftGroup(string groupName);

    /// <summary>
    /// 当前AP状态请求响应
    /// </summary>
    /// <param name="message">响应消息</param>
    /// <returns></returns>
    Task CurrentAPStatusRequested(string message);

    /// <summary>
    /// 错误通知
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <returns></returns>
    Task Error(string message);
}