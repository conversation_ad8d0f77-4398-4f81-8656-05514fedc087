fail: 2025-07-25 09:45:06.9306641 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:45:36.6503203 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:06.5990444 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:46:36.6274436 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:06.6668156 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:47:36.6714429 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:06.6827375 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:48:36.6571028 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:06.6092226 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:49:36.5998241 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:06.6732079 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:50:36.6321051 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:09.1937215 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:51:40.4283034 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:09.8344976 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:52:36.6215635 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:06.5763858 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:53:36.5987747 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:06.5981378 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:54:36.5926188 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:06.5871607 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:55:36.5676822 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:06.6237129 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:56:36.5878814 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:06.5897282 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:57:36.6169390 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:06.6269844 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 888
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:36.9524452 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:58:37.2143221 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:06.5997896 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:06.8701747 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:36.6245684 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 09:59:36.8871002 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:06.5898683 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:06.8959127 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #58
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:36.6203405 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:00:36.8916942 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.5653112 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:06.8455129 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:36.6193177 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:01:36.9261373 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:06.5783453 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:06.8975708 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #39
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:36.6092219 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:02:36.8943728 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #7
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:06.6834582 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:06.9623914 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.5884160 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:03:36.8295578 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:06.6014200 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:06.8809845 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.5609787 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:04:36.8020120 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.6107276 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:06.8517372 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #57
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.6112146 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:05:36.8612852 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.5663317 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:06.8212238 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:38.6275968 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:06:38.8966943 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:08.9676591 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:09.2150781 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #53
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:36.5944899 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:07:36.8963663 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:06.6427438 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:06.9028582 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.5829558 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:08:36.8262796 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #9
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.6025646 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:06.8377213 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.6077294 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:09:36.8568189 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #56
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.5907424 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:06.8433881 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:36.6276029 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:10:36.8975290 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #55
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.5936502 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<string> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      网络请求异常，获取GreenDisplay访问令牌失败      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-25 10:11:06.8328593 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<PagedResponse<APOutput>> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input)
      获取AP列表时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayNetworkException: 网络连接失败，无法获取访问令牌
       ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:7897)
       ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
         at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.Timeout.AsyncTimeoutEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Func`2 timeoutProvider, TimeoutStrategy timeoutStrategy, Func`5 onTimeoutAsync, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, Context context, CancellationToken cancellationToken)
         at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
         at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
         at Microsoft.Extensions.Http.PolicyHttpMessageHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Plugin.GreenDisplay.RefitJsonHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Handler\RefitJsonHandler.cs:line 54
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 127
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAPListAsync(QueryAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 887
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
