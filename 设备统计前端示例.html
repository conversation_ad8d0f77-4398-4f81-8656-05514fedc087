<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备统计示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .percentage {
            font-size: 14px;
            opacity: 0.8;
        }
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .device-type-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .type-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .type-card h4 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .type-stat {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .health-status {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .health-good { background-color: #d4edda; color: #155724; }
        .health-warning { background-color: #fff3cd; color: #856404; }
        .health-critical { background-color: #f8d7da; color: #721c24; }
        .recommendations {
            margin-top: 15px;
        }
        .recommendations ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设备统计监控面板</h1>
        
        <div style="margin-bottom: 20px;">
            <button onclick="loadBasicStats()">加载基础统计</button>
            <button onclick="loadDetailedStats()">加载详细统计</button>
            <button onclick="loadHealthSummary()">加载健康状况</button>
        </div>

        <!-- 基础统计卡片 -->
        <div id="basicStats" class="stats-grid" style="display: none;">
            <div class="stat-card">
                <h3>设备总数</h3>
                <div class="number" id="totalDevices">-</div>
            </div>
            <div class="stat-card">
                <h3>在线设备</h3>
                <div class="number" id="onlineDevices">-</div>
                <div class="percentage" id="onlineRate">-</div>
            </div>
            <div class="stat-card">
                <h3>离线设备</h3>
                <div class="number" id="offlineDevices">-</div>
            </div>
            <div class="stat-card">
                <h3>低电量设备</h3>
                <div class="number" id="lowBatteryDevices">-</div>
                <div class="percentage" id="lowBatteryRate">-</div>
            </div>
            <div class="stat-card">
                <h3>弱信号设备</h3>
                <div class="number" id="weakSignalDevices">-</div>
                <div class="percentage" id="weakSignalRate">-</div>
            </div>
        </div>

        <!-- 健康状况 -->
        <div id="healthStatus" style="display: none;"></div>

        <!-- 设备类型统计 -->
        <div id="deviceTypeStats" class="chart-container" style="display: none;">
            <h3>按设备类型统计</h3>
            <div class="device-type-stats" id="typeStatsContainer"></div>
        </div>

        <!-- 电量分布 -->
        <div id="batteryDistribution" class="chart-container" style="display: none;">
            <h3>电量分布</h3>
            <div id="batteryChart"></div>
        </div>

        <!-- 信号分布 -->
        <div id="signalDistribution" class="chart-container" style="display: none;">
            <h3>信号强度分布</h3>
            <div id="signalChart"></div>
        </div>

        <div id="loading" class="loading" style="display: none;">加载中...</div>
    </div>

    <script>
        // API基础URL（请根据实际情况修改）
        const API_BASE_URL = '/api/devices';
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 加载基础统计
        async function loadBasicStats() {
            showLoading();
            try {
                const response = await fetch(`${API_BASE_URL}/Statistics`);
                const data = await response.json();
                
                if (data) {
                    document.getElementById('totalDevices').textContent = data.TotalDevices || 0;
                    document.getElementById('onlineDevices').textContent = data.OnlineDevices || 0;
                    document.getElementById('offlineDevices').textContent = data.OfflineDevices || 0;
                    document.getElementById('lowBatteryDevices').textContent = data.LowBatteryDevices || 0;
                    document.getElementById('weakSignalDevices').textContent = data.WeakSignalDevices || 0;
                    
                    document.getElementById('onlineRate').textContent = `${data.OnlineRate || 0}%`;
                    document.getElementById('lowBatteryRate').textContent = `${data.LowBatteryRate || 0}%`;
                    document.getElementById('weakSignalRate').textContent = `${data.WeakSignalRate || 0}%`;
                    
                    document.getElementById('basicStats').style.display = 'grid';
                }
            } catch (error) {
                console.error('加载基础统计失败:', error);
                alert('加载基础统计失败，请检查网络连接');
            } finally {
                hideLoading();
            }
        }

        // 加载详细统计
        async function loadDetailedStats() {
            showLoading();
            try {
                const response = await fetch(`${API_BASE_URL}/DetailedStatistics`);
                const data = await response.json();
                
                if (data) {
                    // 显示基础统计
                    if (data.BasicStatistics) {
                        updateBasicStats(data.BasicStatistics);
                        document.getElementById('basicStats').style.display = 'grid';
                    }
                    
                    // 显示设备类型统计
                    if (data.DeviceTypeStatistics) {
                        updateDeviceTypeStats(data.DeviceTypeStatistics);
                        document.getElementById('deviceTypeStats').style.display = 'block';
                    }
                    
                    // 显示电量分布
                    if (data.BatteryDistribution) {
                        updateBatteryDistribution(data.BatteryDistribution);
                        document.getElementById('batteryDistribution').style.display = 'block';
                    }
                    
                    // 显示信号分布
                    if (data.SignalDistribution) {
                        updateSignalDistribution(data.SignalDistribution);
                        document.getElementById('signalDistribution').style.display = 'block';
                    }
                }
            } catch (error) {
                console.error('加载详细统计失败:', error);
                alert('加载详细统计失败，请检查网络连接');
            } finally {
                hideLoading();
            }
        }

        // 加载健康状况摘要
        async function loadHealthSummary() {
            showLoading();
            try {
                const response = await fetch('/api/DeviceStatistics/health-summary');
                const result = await response.json();
                
                if (result.success && result.data) {
                    updateHealthStatus(result.data);
                    document.getElementById('healthStatus').style.display = 'block';
                }
            } catch (error) {
                console.error('加载健康状况失败:', error);
                alert('加载健康状况失败，请检查网络连接');
            } finally {
                hideLoading();
            }
        }

        // 更新基础统计显示
        function updateBasicStats(stats) {
            document.getElementById('totalDevices').textContent = stats.TotalDevices || 0;
            document.getElementById('onlineDevices').textContent = stats.OnlineDevices || 0;
            document.getElementById('offlineDevices').textContent = stats.OfflineDevices || 0;
            document.getElementById('lowBatteryDevices').textContent = stats.LowBatteryDevices || 0;
            document.getElementById('weakSignalDevices').textContent = stats.WeakSignalDevices || 0;
            
            document.getElementById('onlineRate').textContent = `${stats.OnlineRate || 0}%`;
            document.getElementById('lowBatteryRate').textContent = `${stats.LowBatteryRate || 0}%`;
            document.getElementById('weakSignalRate').textContent = `${stats.WeakSignalRate || 0}%`;
        }

        // 更新设备类型统计
        function updateDeviceTypeStats(typeStats) {
            const container = document.getElementById('typeStatsContainer');
            container.innerHTML = '';
            
            typeStats.forEach(type => {
                const typeCard = document.createElement('div');
                typeCard.className = 'type-card';
                typeCard.innerHTML = `
                    <h4>${type.DeviceTypeName}</h4>
                    <div class="type-stat"><span>总数:</span><span>${type.TotalCount}</span></div>
                    <div class="type-stat"><span>在线:</span><span>${type.OnlineCount}</span></div>
                    <div class="type-stat"><span>离线:</span><span>${type.OfflineCount}</span></div>
                    <div class="type-stat"><span>低电量:</span><span>${type.LowBatteryCount}</span></div>
                    <div class="type-stat"><span>弱信号:</span><span>${type.WeakSignalCount}</span></div>
                `;
                container.appendChild(typeCard);
            });
        }

        // 更新电量分布
        function updateBatteryDistribution(battery) {
            const container = document.getElementById('batteryChart');
            container.innerHTML = `
                <div class="type-stat"><span>0-20% (低电量):</span><span>${battery.Battery0To20}</span></div>
                <div class="type-stat"><span>21-50%:</span><span>${battery.Battery21To50}</span></div>
                <div class="type-stat"><span>51-80%:</span><span>${battery.Battery51To80}</span></div>
                <div class="type-stat"><span>81-100%:</span><span>${battery.Battery81To100}</span></div>
                <div class="type-stat"><span>平均电量:</span><span>${battery.AverageBattery}%</span></div>
            `;
        }

        // 更新信号分布
        function updateSignalDistribution(signal) {
            const container = document.getElementById('signalChart');
            container.innerHTML = `
                <div class="type-stat"><span>< -80dBm (弱信号):</span><span>${signal.SignalBelow80}</span></div>
                <div class="type-stat"><span>-80 到 -60dBm:</span><span>${signal.Signal80To60}</span></div>
                <div class="type-stat"><span>-60 到 -40dBm:</span><span>${signal.Signal60To40}</span></div>
                <div class="type-stat"><span>> -40dBm (强信号):</span><span>${signal.SignalAbove40}</span></div>
                <div class="type-stat"><span>平均信号:</span><span>${signal.AverageSignal}dBm</span></div>
            `;
        }

        // 更新健康状况
        function updateHealthStatus(health) {
            const container = document.getElementById('healthStatus');
            const statusClass = health.HealthStatus === '良好' ? 'health-good' : 
                               health.HealthStatus === '警告' ? 'health-warning' : 'health-critical';
            
            let issuesHtml = '';
            if (health.Issues && health.Issues.length > 0) {
                issuesHtml = '<h4>发现的问题:</h4><ul>' + 
                           health.Issues.map(issue => `<li>${issue}</li>`).join('') + 
                           '</ul>';
            }
            
            let recommendationsHtml = '';
            if (health.Recommendations && health.Recommendations.length > 0) {
                recommendationsHtml = '<div class="recommendations"><h4>维护建议:</h4><ul>' + 
                                    health.Recommendations.map(rec => `<li>${rec}</li>`).join('') + 
                                    '</ul></div>';
            }
            
            container.className = `health-status ${statusClass}`;
            container.innerHTML = `
                <h3>设备健康状况: ${health.HealthStatus}</h3>
                <p>总设备数: ${health.TotalDevices} | 在线率: ${health.OnlineRate}%</p>
                ${issuesHtml}
                ${recommendationsHtml}
            `;
        }

        // 页面加载完成后自动加载基础统计
        document.addEventListener('DOMContentLoaded', function() {
            loadBasicStats();
        });
    </script>
</body>
</html>
