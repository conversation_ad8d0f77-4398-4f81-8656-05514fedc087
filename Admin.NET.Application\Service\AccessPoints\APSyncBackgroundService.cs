// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Entity;
using Admin.NET.Plugin.GreenDisplay.Service;
using Furion.DatabaseAccessor;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.NET.Application.Service;

/// <summary>
/// AP状态同步后台服务
/// </summary>
public class APSyncBackgroundService : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<APSyncBackgroundService> _logger;
    private Timer _syncTimer;
    private readonly TimeSpan _syncInterval = TimeSpan.FromSeconds(30); // 30秒同步一次

    public APSyncBackgroundService(
        IServiceScopeFactory serviceScopeFactory,
        ILogger<APSyncBackgroundService> logger)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns></returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("AP状态同步后台服务启动，同步间隔：{Interval}秒", _syncInterval.TotalSeconds);

        // 等待应用程序完全启动
        await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);

        // 创建定时器
        _syncTimer = new Timer(
            callback: async _ => await SyncAPStatusAsync(),
            state: null,
            dueTime: TimeSpan.Zero, // 立即开始第一次同步
            period: _syncInterval);

        try
        {
            // 保持服务运行直到取消
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AP状态同步后台服务正在停止...");
        }
        finally
        {
            _syncTimer?.Dispose();
        }
    }

    /// <summary>
    /// 同步AP状态的核心方法
    /// </summary>
    /// <returns></returns>
    private async Task SyncAPStatusAsync()
    {
        _logger.LogDebug("开始同步AP状态");

        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var services = scope.ServiceProvider;

            var accessPointsRep = services.GetRequiredService<SqlSugarRepository<AccessPoints>>();
            var greenDisplayService = services.GetRequiredService<GreenDisplayService>();
            var hubContext = services.GetRequiredService<IHubContext<APStatusHub>>();

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(25)); // 设置超时时间略小于定时器间隔

            // 获取第三方平台的AP数据
            var thirdPartyAPs = await GetAllThirdPartyAPsAsync(greenDisplayService, cts.Token);

            if (thirdPartyAPs == null || thirdPartyAPs.Count == 0)
            {
                _logger.LogDebug("第三方平台未返回AP数据");
                return;
            }

            // 获取本地AP数据
            var localAPs = await accessPointsRep.AsQueryable().ToListAsync();
            var localAPDict = localAPs.ToDictionary(ap => ap.mac_address, ap => ap);

            var updatedAPs = new List<AccessPoints>();

            // 比较并更新状态
            foreach (var thirdPartyAP in thirdPartyAPs)
            {
                if (string.IsNullOrEmpty(thirdPartyAP.ApMac) || !localAPDict.TryGetValue(thirdPartyAP.ApMac, out var localAP))
                    continue;

                var newStatus = thirdPartyAP.Status;
                var hasStatusChange = localAP.ap_status != newStatus;
                var hasIpChange = !string.IsNullOrEmpty(thirdPartyAP.ApIp) && localAP.ip_address != thirdPartyAP.ApIp;
                var hasVersionChange = !string.IsNullOrEmpty(thirdPartyAP.Version) && localAP.firmware_version != thirdPartyAP.Version;
                //46:C1:01:01:22:87
                if (hasStatusChange || hasIpChange || hasVersionChange)
                {
                    if (hasStatusChange)
                    {
                        var oldStatus = localAP.ap_status;
                        localAP.ap_status = newStatus;
                        _logger.LogDebug("AP状态变更：MAC={Mac}, 旧状态={OldStatus}, 新状态={NewStatus}",
                            thirdPartyAP.ApMac, oldStatus?.ToString() ?? "null", newStatus.ToString());
                    }
                    if (hasIpChange)
                    {
                        localAP.ip_address = thirdPartyAP.ApIp;
                        _logger.LogDebug("AP IP变更：MAC={Mac}, 新IP={NewIp}", thirdPartyAP.ApMac, thirdPartyAP.ApIp);
                    }

                    if (hasVersionChange)
                    {
                        localAP.firmware_version = thirdPartyAP.Version;
                        _logger.LogDebug("AP版本变更：MAC={Mac}, 新版本={NewVersion}", thirdPartyAP.ApMac, thirdPartyAP.Version);
                    }

                    updatedAPs.Add(localAP);
                }
            }

            // 批量更新数据库
            if (updatedAPs.Count > 0)
            {
                await accessPointsRep.UpdateRangeAsync(updatedAPs);
                _logger.LogInformation("批量更新AP状态完成，更新数量：{Count}", updatedAPs.Count);

                // 通过SignalR推送状态变更通知
                await NotifyAPStatusChangesAsync(hubContext, updatedAPs);
            }
            else
            {
                _logger.LogDebug("无AP状态变更");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("同步AP状态操作超时");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步AP状态时发生异常");
        }
    }

    /// <summary>
    /// 获取第三方平台所有AP数据
    /// </summary>
    /// <param name="greenDisplayService">绿显服务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>AP数据列表</returns>
    private async Task<List<APOutput>> GetAllThirdPartyAPsAsync(GreenDisplayService greenDisplayService, CancellationToken cancellationToken)
    {
        var allAPs = new List<APOutput>();
        int pageNo = 1;
        const int pageSize = 50; // 每页获取50条数据

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var queryInput = new QueryAPInput
                {
                    PageNo = pageNo,
                    PageSize = pageSize
                };

                var response = await greenDisplayService.GetAPListAsync(queryInput);

                if (response?.list == null || response.list.Count == 0)
                {
                    _logger.LogDebug("第 {PageNo} 页无数据，结束获取", pageNo);
                    break;
                }

                allAPs.AddRange(response.list);

                _logger.LogDebug("获取第 {PageNo} 页数据成功，本页 {PageCount} 条，总计 {TotalCount} 条",
                    pageNo, response.list.Count, allAPs.Count);

                // 如果当前页数据少于页大小，说明已经是最后一页
                if (response.list.Count < pageSize)
                {
                    break;
                }

                pageNo++;

                // 添加延迟避免频繁请求
                await Task.Delay(100, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取第三方平台AP数据时发生异常，页码: {PageNo}", pageNo);
                throw;
            }
        }

        return allAPs;
    }

    /// <summary>
    /// 将状态字符串映射为整数
    /// </summary>
    /// <param name="status">状态字符串</param>
    /// <returns>状态整数值</returns>
    private int? MapStatusToInt(string status)
    {
        if (string.IsNullOrEmpty(status))
            return null;

        return status.ToLower() switch
        {
            "online" or "在线" => 1,
            "offline" or "离线" => 2,
            "error" or "异常" => 3,
            _ => null
        };
    }

    /// <summary>
    /// 通过SignalR推送AP状态变更通知
    /// </summary>
    /// <param name="hubContext">Hub上下文</param>
    /// <param name="updatedAPs">更新的AP列表</param>
    /// <returns></returns>
    private async Task NotifyAPStatusChangesAsync(IHubContext<APStatusHub> hubContext, List<AccessPoints> updatedAPs)
    {
        try
        {
            var statusUpdates = updatedAPs.Select(ap => new
            {
                MacAddress = ap.mac_address,
                ApName = ap.ap_name,
                Status = ap.ap_status,
                IpAddress = ap.ip_address,
                FirmwareVersion = ap.firmware_version,
                UpdateTime = DateTime.Now
            }).ToList();

            // 向所有连接的客户端推送状态更新
            await hubContext.Clients.All.SendAsync("APStatusUpdated", statusUpdates);

            _logger.LogDebug("已通过SignalR推送AP状态变更通知，数量：{Count}", updatedAPs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送AP状态变更通知时发生异常");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("AP状态同步后台服务正在停止...");
        
        _syncTimer?.Dispose();
        
        await base.StopAsync(cancellationToken);
        
        _logger.LogInformation("AP状态同步后台服务已停止");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _syncTimer?.Dispose();
        base.Dispose();
    }
}