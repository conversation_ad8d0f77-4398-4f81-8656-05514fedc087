// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
using Admin.NET.Plugin.GreenDisplay.Service;
using Microsoft.Extensions.Logging;
namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class DevicesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Devices> _devicesRep;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<DevicesService> _logger;

    public DevicesService(SqlSugarRepository<Devices> devicesRep, GreenDisplayService greenDisplayService, ILogger<DevicesService> logger)
    {
        _devicesRep = devicesRep;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询蓝牙桌牌设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DevicesOutput>> Page(PageDevicesInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _devicesRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.device_name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.device_name), u => u.device_name.Contains(input.device_name.Trim()))
            .WhereIF(input.device_type != null, u => u.device_type == input.device_type)
            .WhereIF(input.status != null, u => u.status == input.status)
            .Select<DevicesOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取蓝牙桌牌设备详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取蓝牙桌牌设备详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Devices> Detail([FromQuery] QueryByIdDevicesInput input)
    {
        return await _devicesRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加蓝牙桌牌设备 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDevicesInput input)
    {
        var entity = input.Adapt<Devices>();

        return await _devicesRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新蓝牙桌牌设备 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDevicesInput input)
    {
        var entity = input.Adapt<Devices>();
        await _devicesRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDevicesInput input)
    {
        var entity = await _devicesRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _devicesRep.FakeDeleteAsync(entity);   //假删除
        //await _devicesRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteDevicesInput> input)
    {
        var exp = Expressionable.Create<Devices>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _devicesRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _devicesRep.FakeDeleteAsync(list);   //假删除
        //return await _devicesRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取设备统计信息 📊
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取设备统计信息")]
    [ApiDescriptionSettings(Name = "Statistics"), HttpGet]
    public async Task<DeviceStatisticsOutput> GetDeviceStatistics()
    {
        try
        {
            // 查询所有未删除的设备
            var allDevices = await _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete)
                .ToListAsync();
            var totalDevices = allDevices.Count;
            if (totalDevices == 0)
            {
                return new DeviceStatisticsOutput
                {
                    TotalDevices = 0,
                    OnlineDevices = 0,
                    OfflineDevices = 0,
                    LowBatteryDevices = 0,
                    WeakSignalDevices = 0,
                    OnlineRate = 0,
                    LowBatteryRate = 0,
                    WeakSignalRate = 0
                };
            }
            // 统计在线设备（status = 0表示在线）
            var onlineDevices = allDevices.Count(d => d.status == 0);

            // 统计离线设备（status = 1表示离线）
            var offlineDevices = allDevices.Count(d => d.status == 1);

            // 统计低电量设备（电量低于20%）
            var lowBatteryDevices = allDevices.Count(d => d.battery_level.HasValue && d.battery_level < 20);

            // 统计弱信号设备（信号弱于-80dBm）
            var weakSignalDevices = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80);

            // 计算百分比
            var onlineRate = totalDevices > 0 ? Math.Round((decimal)onlineDevices / totalDevices * 100, 2) : 0;
            var lowBatteryRate = totalDevices > 0 ? Math.Round((decimal)lowBatteryDevices / totalDevices * 100, 2) : 0;
            var weakSignalRate = totalDevices > 0 ? Math.Round((decimal)weakSignalDevices / totalDevices * 100, 2) : 0;

            var result = new DeviceStatisticsOutput
            {
                TotalDevices = totalDevices,
                OnlineDevices = onlineDevices,
                OfflineDevices = offlineDevices,
                LowBatteryDevices = lowBatteryDevices,
                WeakSignalDevices = weakSignalDevices,
                OnlineRate = onlineRate,
                LowBatteryRate = lowBatteryRate,
                WeakSignalRate = weakSignalRate
            };

            _logger.LogInformation("设备统计查询成功，总设备数：{TotalDevices}，在线设备：{OnlineDevices}，低电量设备：{LowBatteryDevices}，弱信号设备：{WeakSignalDevices}",
                totalDevices, onlineDevices, lowBatteryDevices, weakSignalDevices);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备统计信息时发生错误");
            throw Oops.Oh("获取设备统计信息失败");
        }
    }

    /// <summary>
    /// 获取设备详细统计信息 📊
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取设备详细统计信息")]
    [ApiDescriptionSettings(Name = "DetailedStatistics"), HttpGet]
    public async Task<DeviceDetailedStatisticsOutput> GetDeviceDetailedStatistics()
    {
        try
        {
            // 查询所有未删除的设备
            var allDevices = await _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete)
                .ToListAsync();

            // 获取基础统计信息
            var basicStats = await GetDeviceStatistics();

            // 按设备类型分组统计
            var deviceTypeStats = allDevices
                .GroupBy(d => d.device_type ?? 0)
                .Select(g => new DeviceTypeStatistics
                {
                    DeviceType = g.Key,
                    DeviceTypeName = g.Key == 1 ? "姓名桌牌" : g.Key == 2 ? "价格标签" : "未知类型",
                    TotalCount = g.Count(),
                    OnlineCount = g.Count(d => d.status == 0),
                    OfflineCount = g.Count(d => d.status == 1),
                    LowBatteryCount = g.Count(d => d.battery_level.HasValue && d.battery_level < 20),
                    WeakSignalCount = g.Count(d => d.signal_strength.HasValue && d.signal_strength < -80)
                })
                .ToList();

            // 电量分布统计
            var batteryStats = new BatteryDistributionStatistics
            {
                Battery0To20 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level >= 0 && d.battery_level <= 20),
                Battery21To50 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 20 && d.battery_level <= 50),
                Battery51To80 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 50 && d.battery_level <= 80),
                Battery81To100 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 80 && d.battery_level <= 100),
                AverageBattery = allDevices.Where(d => d.battery_level.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.battery_level.HasValue).Average(d => d.battery_level.Value), 2)
                    : 0
            };

            // 信号强度分布统计
            var signalStats = new SignalDistributionStatistics
            {
                SignalBelow80 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80),
                Signal80To60 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -80 && d.signal_strength < -60),
                Signal60To40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -60 && d.signal_strength < -40),
                SignalAbove40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -40),
                AverageSignal = allDevices.Where(d => d.signal_strength.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.signal_strength.HasValue).Average(d => d.signal_strength.Value), 2)
                    : 0
            };

            var result = new DeviceDetailedStatisticsOutput
            {
                BasicStatistics = basicStats,
                DeviceTypeStatistics = deviceTypeStats,
                BatteryDistribution = batteryStats,
                SignalDistribution = signalStats
            };

            _logger.LogInformation("设备详细统计查询成功，总设备数：{TotalDevices}，设备类型数：{TypeCount}",
                basicStats.TotalDevices, deviceTypeStats.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备详细统计信息时发生错误");
            throw Oops.Oh("获取设备详细统计信息失败");
        }
    }

    /// <summary>
    /// 根据条件获取设备统计信息 📊
    /// </summary>
    /// <param name="input">统计查询条件</param>
    /// <returns></returns>
    [DisplayName("根据条件获取设备统计信息")]
    [ApiDescriptionSettings(Name = "StatisticsByCondition"), HttpPost]
    public async Task<DeviceDetailedStatisticsOutput> GetDeviceStatisticsByCondition(DeviceStatisticsInput input)
    {
        try
        {
            // 构建查询条件
            var query = _devicesRep.AsQueryable()
                .Where(d => !d.IsDelete);

            // 按时间范围过滤
            if (input.StartTime.HasValue)
            {
                query = query.Where(d => d.CreateTime >= input.StartTime.Value);
            }

            if (input.EndTime.HasValue)
            {
                query = query.Where(d => d.CreateTime <= input.EndTime.Value);
            }

            // 按设备类型过滤
            if (input.DeviceType.HasValue)
            {
                query = query.Where(d => d.device_type == input.DeviceType.Value);
            }

            // 按网关ID过滤
            if (input.ApId.HasValue)
            {
                query = query.Where(d => d.ap_id == input.ApId.Value);
            }

            var allDevices = await query.ToListAsync();
            var totalDevices = allDevices.Count;

            if (totalDevices == 0)
            {
                return new DeviceDetailedStatisticsOutput
                {
                    BasicStatistics = new DeviceStatisticsOutput(),
                    DeviceTypeStatistics = [],
                    BatteryDistribution = new BatteryDistributionStatistics(),
                    SignalDistribution = new SignalDistributionStatistics()
                };
            }

            // 计算基础统计
            var onlineDevices = allDevices.Count(d => d.status == 0);
            var offlineDevices = allDevices.Count(d => d.status == 1);
            var lowBatteryDevices = allDevices.Count(d => d.battery_level.HasValue && d.battery_level < 20);
            var weakSignalDevices = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80);

            var basicStats = new DeviceStatisticsOutput
            {
                TotalDevices = totalDevices,
                OnlineDevices = onlineDevices,
                OfflineDevices = offlineDevices,
                LowBatteryDevices = lowBatteryDevices,
                WeakSignalDevices = weakSignalDevices,
                OnlineRate = totalDevices > 0 ? Math.Round((decimal)onlineDevices / totalDevices * 100, 2) : 0,
                LowBatteryRate = totalDevices > 0 ? Math.Round((decimal)lowBatteryDevices / totalDevices * 100, 2) : 0,
                WeakSignalRate = totalDevices > 0 ? Math.Round((decimal)weakSignalDevices / totalDevices * 100, 2) : 0
            };

            // 按设备类型分组统计
            var deviceTypeStats = allDevices
                .GroupBy(d => d.device_type ?? 0)
                .Select(g => new DeviceTypeStatistics
                {
                    DeviceType = g.Key,
                    DeviceTypeName = g.Key == 1 ? "姓名桌牌" : g.Key == 2 ? "价格标签" : "未知类型",
                    TotalCount = g.Count(),
                    OnlineCount = g.Count(d => d.status == 0),
                    OfflineCount = g.Count(d => d.status == 1),
                    LowBatteryCount = g.Count(d => d.battery_level.HasValue && d.battery_level < 20),
                    WeakSignalCount = g.Count(d => d.signal_strength.HasValue && d.signal_strength < -80)
                })
                .ToList();

            // 电量分布统计
            var batteryStats = new BatteryDistributionStatistics
            {
                Battery0To20 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level >= 0 && d.battery_level <= 20),
                Battery21To50 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 20 && d.battery_level <= 50),
                Battery51To80 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 50 && d.battery_level <= 80),
                Battery81To100 = allDevices.Count(d => d.battery_level.HasValue && d.battery_level > 80 && d.battery_level <= 100),
                AverageBattery = allDevices.Where(d => d.battery_level.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.battery_level.HasValue).Average(d => d.battery_level.Value), 2)
                    : 0
            };

            // 信号强度分布统计
            var signalStats = new SignalDistributionStatistics
            {
                SignalBelow80 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength < -80),
                Signal80To60 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -80 && d.signal_strength < -60),
                Signal60To40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -60 && d.signal_strength < -40),
                SignalAbove40 = allDevices.Count(d => d.signal_strength.HasValue && d.signal_strength >= -40),
                AverageSignal = allDevices.Where(d => d.signal_strength.HasValue).Any()
                    ? Math.Round((decimal)allDevices.Where(d => d.signal_strength.HasValue).Average(d => d.signal_strength.Value), 2)
                    : 0
            };

            var result = new DeviceDetailedStatisticsOutput
            {
                BasicStatistics = basicStats,
                DeviceTypeStatistics = deviceTypeStats,
                BatteryDistribution = batteryStats,
                SignalDistribution = signalStats
            };

            _logger.LogInformation("条件设备统计查询成功，查询条件：设备类型={DeviceType}，网关ID={ApId}，时间范围={StartTime}-{EndTime}，结果：总设备数={TotalDevices}",
                input.DeviceType, input.ApId, input.StartTime, input.EndTime, totalDevices);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件获取设备统计信息时发生错误，查询条件：{@Input}", input);
            throw Oops.Oh("获取设备统计信息失败");
        }
    }
}
